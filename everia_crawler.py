import asyncio
import json
import os
import re
import sys
import time
from functools import partial
from typing import Dict, Any, List, Tuple, Optional
from urllib.parse import urljoin, unquote, quote

import aiofiles
import aiofiles.os
import httpx
import uvicorn
from bs4 import BeautifulSoup
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.responses import FileResponse, HTMLResponse, Response
from loguru import logger
import aiosqlite
try:
    import pyppeteer
    PYPPETEER_AVAILABLE = True
except ImportError:
    PYPPETEER_AVAILABLE = False


# --- 核心配置与全局变量 ---
SAVE_DIR = 'downloaded'
DB_FILE = 'download_state.db'
LOG_FILE = "download_log.txt"
ERROR_LOG_FILE = "error_log.json" # 【新增】错误日志文件名
CATEGORIES: Dict[str, str] = {
    'korea': 'https://everia.club/category/korea/', 'cosplay': 'https://everia.club/category/cosplay/',
    'japan': 'https://everia.club/category/japan/', 'gravure': 'https://everia.club/category/gravure/',
    'chinese': 'https://everia.club/category/chinese/','thailand': 'https://everia.club/category/thailand/',
}
HTTP_TIMEOUT = 30
MAX_CONCURRENT_ALBUMS = 25
PAGE_SCAN_CONCURRENCY = 25
DEFAULT_IMAGE_CONCURRENCY = 15

PYPPETEER_PAGE_TIMEOUT = 20000
PYPPETEER_WAIT_DELAY = 3000

# --- 异步状态管理与通信 ---
CRAWL_RUNNING = asyncio.Event()
broadcast_queue = asyncio.Queue()
stats_update_queue = asyncio.Queue()
shared_state: Dict[str, Any] = {
    "progress": {}, "stats": {"totalAlbums": 0, "totalImages": 0, "totalSize": 0, "speed": 0.0},
    "status": "空闲", "is_crawling": False, "is_organizing": False,
    "current_category": "", "concurrency": DEFAULT_IMAGE_CONCURRENCY,
    "lock": asyncio.Lock(),
    "browser": None
}
# 【新增】用于防止并发写错误日志文件的锁
error_log_lock = asyncio.Lock()

# --- 日志配置 ---
logger.remove()
logger.add(sys.stdout, level="INFO", format="<green>{time:HH:mm:ss}</green>| <level>{level: <7}</level>| <level></level>")
logger.add(LOG_FILE, encoding='utf-8', level="DEBUG", rotation="10 MB")

async def log_to_ui(message: str, m_type: str = "info"):
    color_map = {"info": "#e0e0e0", "success": "#4caf50", "error": "#f44336", "warning": "#ff9800", "primary": "#00bcd4"}
    html_message = f'<span style="color:{color_map.get(m_type, "#e0e0e0")};">{time.strftime("%H:%M:%S")} | {message}</span>'
    plain_message = f"{time.strftime('%H:%M:%S')} | {re.sub('<[^<]+?>', '', message)}"
    await broadcast_queue.put({"type": "log", "html": html_message, "plain": plain_message})

# --- WebSocket 管理器 ---
class WebSocketManager:
    def __init__(self): self.active_connections: list[WebSocket] = []
    async def connect(self, ws: WebSocket): await ws.accept(); self.active_connections.append(ws)
    def disconnect(self, ws: WebSocket): self.active_connections.remove(ws)
    async def broadcast(self, data: dict):
        if self.active_connections:
            await asyncio.gather(*[c.send_json(data) for c in self.active_connections], return_exceptions=True)

ws_manager = WebSocketManager()
app = FastAPI()

# --- 前端代码 (V16.4) ---
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Everia Crawler V16.4 (Pipeline)</title>
    <style>
        :root{--bg-color:#1a1a1a;--primary-color:#00bcd4;--text-color:#e0e0e0;--card-bg:#2c2c2c;--border-color:#444;--success-color:#4caf50;--error-color:#f44336;--warning-color:#ff9800;--font-family:'Segoe UI',Roboto,sans-serif}
        *{box-sizing:border-box;margin:0;padding:0}
        body{background-color:var(--bg-color);color:var(--text-color);font-family:var(--font-family);line-height:1.6;font-size:16px;overflow:hidden}
        .container{max-width:1600px;margin:20px auto;padding:0 20px}
        #loader{position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(26,26,26,0.95);z-index:9999;display:flex;justify-content:center;align-items:center;flex-direction:column;transition:opacity .5s}
        #loader.hidden{opacity:0;pointer-events:none}
        .spinner{width:50px;height:50px;border:5px solid var(--border-color);border-top-color:var(--primary-color);border-radius:50%;animation:spin 1s linear infinite}
        #loader-status{margin-top:20px;font-size:1.2em;color:var(--primary-color)}
        @keyframes spin{to{transform:rotate(360deg)}}
        .tabs{display:flex;border-bottom:2px solid var(--border-color);margin-bottom:20px}
        .tab-button{padding:10px 20px;cursor:pointer;background:none;border:none;color:var(--text-color);font-size:1.1em;opacity:.7;transition:all .3s ease;border-bottom:3px solid transparent}
        .tab-button.active{opacity:1;border-bottom-color:var(--primary-color)}
        .tab-content{display:none}.tab-content.active{display:block}
        .grid{display:grid;grid-template-columns:1fr 1fr;gap:20px;align-items:start}
        .card{background:var(--card-bg);border-radius:8px;padding:20px;border:1px solid var(--border-color)}
        h1,h2{color:var(--primary-color);margin-bottom:15px}
        button{background-color:var(--primary-color);color:#fff;border:none;padding:10px 20px;border-radius:5px;cursor:pointer;font-size:1em;transition:all .3s ease;margin-right:10px;position:relative}
        button:hover:not(:disabled){filter:brightness(1.2)}
        button:disabled{background-color:#555;cursor:not-allowed}
        button .btn-text.hidden{visibility:hidden}
        button .btn-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);display:none;width:20px;height:20px;border:2px solid #fff;border-top-color:transparent;border-radius:50%;animation:spin .6s linear infinite}
        button.loading .btn-loader{display:block}
        .log-container{height:400px;overflow-y:scroll;background-color:#111;padding:15px;border-radius:5px;font-family:monospace;font-size:.9em;white-space:pre-wrap;margin-top:20px}
        .stats-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(120px,1fr));gap:15px}
        .stat-item{background:#333;padding:15px;border-radius:5px;text-align:center}
        .stat-item .label{font-size:.9em;opacity:.7}.stat-item .value{font-size:1.5em;font-weight:700}
        .progress-list{max-height:350px;overflow-y:auto;margin-top:20px;padding-right:10px}
        .progress-item .progress-bar-bg{background-color:#444;border-radius:5px;height:20px;overflow:hidden;width:100%}
        .progress-item .progress-bar{background-color:var(--success-color);height:100%;transition:width .2s ease;text-align:center;color:#fff;font-size:.8em;line-height:20px}
        .progress-item .progress-label{font-size:.9em;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;margin-bottom:5px}
        .gallery-grid{column-count:5;column-gap:15px;min-height:200px}
        .album-card{background:var(--card-bg);border-radius:8px;overflow:hidden;cursor:pointer;transition:transform .3s,box-shadow .3s;border:1px solid var(--border-color);margin-bottom:15px;display:inline-block;width:100%}
        .album-card:hover{transform:translateY(-5px);box-shadow:0 10px 20px rgba(0,188,212,.3)}
        .album-card img{width:100%;height:auto;display:block;background-color:#333}
        .album-card .title{padding:10px;font-size:.9em;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}
        .lightbox{position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,.9);display:flex;justify-content:center;align-items:center;z-index:1000;opacity:0;visibility:hidden;transition:opacity .3s}
        .lightbox.show{opacity:1;visibility:visible}
        .lightbox img{max-width:90vw;max-height:90vh;object-fit:contain}
        .lightbox .close{position:absolute;top:20px;right:30px;font-size:3em;cursor:pointer;user-select:none}
        .category-checklist{display:grid;grid-template-columns:repeat(auto-fill,minmax(150px,1fr));gap:10px;margin-bottom:20px}
        .category-checklist label{background:#333;padding:8px 12px;border-radius:5px;cursor:pointer;display:flex;align-items:center}
        .category-checklist input{margin-right:10px}
        #sparkline{stroke:var(--primary-color);stroke-width:2;fill:rgba(0,188,212,0.2)}
    </style>
</head>
<body>
<div id="loader"><div class="spinner"></div><p id="loader-status">正在连接服务器...</p></div>
<div id="app" class="container">
    <h1>Everia Crawler <span style="font-size: 0.5em; color: var(--primary-color);">V16.4 Pipeline</span></h1>
    <div class="tabs"><button class="tab-button active" data-tab="crawler">爬虫控制台</button><button class="tab-button" data-tab="gallery">画廊鉴赏</button></div>
    <div id="crawler" class="tab-content active"><div class="grid"><div><div class="card"><h2>控制与设置</h2><div id="category-checklist-container"><h3>选择要爬取的分类</h3><div id="category-checklist" class="category-checklist"></div></div><div style="margin-top:20px;"><button id="start-crawl-btn"><span class="btn-text">开始爬取</span><span class="btn-loader"></span></button><button id="stop-crawl-btn" disabled><span class="btn-text">停止爬取</span><span class="btn-loader"></span></button><button id="organize-files-btn"><span class="btn-text">整理所有文件</span><span class="btn-loader"></span></button></div><div style="margin-top: 20px;"><label for="concurrency-slider">单个图集内部图片并发数: <span id="concurrency-value">10</span></label><input type="range" id="concurrency-slider" min="1" max="100" step="1" value="10" style="width:100%;"></div></div><div class="card" style="margin-top: 20px;"><h2>UI 日志</h2><div id="log-container" class="log-container"></div></div></div><div><div class="card"><h2>实时统计</h2><div id="stats-grid" class="stats-grid"><div class="stat-item"><div class="label">任务状态</div><div class="value" id="stat-status">空闲</div></div><div class="stat-item"><div class="label">当前分类</div><div class="value" id="stat-current-category">N/A</div></div><div class="stat-item"><div class="label">下载图集</div><div class="value" id="stat-total-albums">0</div></div><div class="stat-item"><div class="label">下载图片</div><div class="value" id="stat-total-images">0</div></div><div class="stat-item"><div class="label">总大小</div><div class="value" id="stat-total-size">0 B</div></div><div class="stat-item"><div class="label">下载速率</div><div class="value" id="stat-speed" style="color:var(--success-color);">0 B/s</div></div></div><h3 style="margin-top: 20px;">速率图表 (SVG)</h3><svg id="speed-chart" width="100%" height="100" viewBox="0 0 300 100" preserveAspectRatio="none"><path id="sparkline" d="M0,100 L300,100"/></svg></div><div class="card" style="margin-top: 20px;"><h2>下载队列</h2><div id="progress-list" class="progress-list"><div id="progress-placeholder" class="gallery-placeholder">暂无下载任务</div></div></div></div></div></div>
    <div id="gallery" class="tab-content"><div class="card"><h2>画廊</h2><div class="filter-bar" style="margin-bottom:20px;"><label for="category-filter">分类: </label><select id="category-filter" style="padding:8px;background:var(--card-bg);color:var(--text-color);border:1px solid var(--border-color);border-radius:5px"></select></div><div id="gallery-grid" class="gallery-grid"><div id="gallery-placeholder" class="gallery-placeholder">画廊为空</div></div></div></div>
    <div id="lightbox" class="lightbox"><span class="close" id="lightbox-close">×</span><img id="lightbox-img" src=""></div>
</div>
<script>
(() => {
    const $ = (s) => document.querySelector(s);
    const $$ = (s) => document.querySelectorAll(s);

    const dom = {
        loader: $('#loader'), loaderStatus: $('#loader-status'),
        tabs: $$('.tab-button'), tabContents: $$('.tab-content'), logContainer: $('#log-container'),
        startBtn: $('#start-crawl-btn'), stopBtn: $('#stop-crawl-btn'), organizeBtn: $('#organize-files-btn'),
        concurrencySlider: $('#concurrency-slider'), concurrencyValue: $('#concurrency-value'),
        categoryChecklist: $('#category-checklist'),
        stats: { status: $('#stat-status'), currentCategory: $('#stat-current-category'), totalAlbums: $('#stat-total-albums'), totalImages: $('#stat-total-images'), totalSize: $('#stat-total-size'), speed: $('#stat-speed') },
        progressList: $('#progress-list'), progressPlaceholder: $('#progress-placeholder'),
        gallery: { grid: $('#gallery-grid'), filter: $('#category-filter'), placeholder: $('#gallery-placeholder') },
        lightbox: { container: $('#lightbox'), closeBtn: $('#lightbox-close'), image: $('#lightbox-img') },
        sparkline: $('#sparkline') };
    
    let ws;
    let state = { isCrawling: false, isOrganizing: false, speedHistory: new Array(30).fill(0), galleries: {} };

    const setButtonLoading = (btn, isLoading) => { if (!btn) return; btn.classList.toggle('loading', isLoading); const isAnyTaskRunning = state.isCrawling || state.isOrganizing; if (btn === dom.stopBtn) { btn.disabled = isLoading || !state.isCrawling; } else { btn.disabled = isAnyTaskRunning || isLoading; } btn.querySelector('.btn-text').classList.toggle('hidden', isLoading); };
    const formatBytes = (b, d = 2) => { if (!b || b <= 0) return '0 B'; const k = 1024, s = ['B', 'KB', 'MB', 'GB', 'TB'], i = Math.floor(Math.log(b) / Math.log(k)); return `${parseFloat((b / Math.pow(k, i)).toFixed(d))} ${s[i]}` };
    const addLog = (html, plainText) => { dom.logContainer.insertAdjacentHTML('beforeend', `<p>${html}</p>`); if (dom.logContainer.children.length > 500) { dom.logContainer.firstElementChild.remove(); } dom.logContainer.scrollTop = dom.logContainer.scrollHeight; console.log(plainText); };
    const updateSparkline = () => { const max = Math.max(...state.speedHistory, 1), w = 300, h = 100; const p = state.speedHistory.map((d, i) => `${(i / 29) * w},${h - (d / max) * h}`).join(' L '); dom.sparkline.setAttribute('d', `M ${p} L ${w},${h} L 0,${h} Z`) };
    const updateStats = (s) => { dom.stats.totalAlbums.textContent = s.totalAlbums; dom.stats.totalImages.textContent = s.totalImages; dom.stats.totalSize.textContent = formatBytes(s.totalSize); dom.stats.speed.textContent = `${formatBytes(s.speed)}/s`; state.speedHistory.push(s.speed || 0); if(state.speedHistory.length>30)state.speedHistory.shift(); updateSparkline() };
    const updateProgress = (p) => { dom.progressPlaceholder.style.display = Object.keys(p).length === 0 ? 'flex' : 'none'; $$('#progress-list .progress-item').forEach(i => { if (!p[i.dataset.album]) i.remove() }); for (const n in p) { const d = p[n]; let i = $(`#progress-list .progress-item[data-album="${n}"]`); if (!i) { i = document.createElement('div'); i.className = 'progress-item'; i.dataset.album = n; i.innerHTML = `<div class="progress-label"></div><div class="progress-bar-bg"><div class="progress-bar"></div></div>`; dom.progressList.appendChild(i) } const pct = d.total > 0 ? (d.completed / d.total) * 100 : 0; i.querySelector('.progress-bar').style.width = `${pct}%`; i.querySelector('.progress-label').textContent = `${n} (${d.completed}/${d.total})` } };
    const setAppBusyState = (isCrawling, isOrganizing) => {
        state.isCrawling = isCrawling; state.isOrganizing = isOrganizing;
        const isAnyTaskRunning = isCrawling || isOrganizing;
        dom.startBtn.disabled = isAnyTaskRunning; dom.organizeBtn.disabled = isAnyTaskRunning; dom.concurrencySlider.disabled = isAnyTaskRunning;
        dom.stopBtn.disabled = !isCrawling;
        setButtonLoading(dom.startBtn, isCrawling); setButtonLoading(dom.organizeBtn, isOrganizing);
    };
    const updateStatusText = (status = '空闲', currentCategory = 'N/A') => { dom.stats.status.textContent = status; dom.stats.currentCategory.textContent = currentCategory || 'N/A'; };
    const renderGallery = () => { const sel = dom.gallery.filter.value, albums = (sel === 'all') ? Object.values(state.galleries).flat().sort((a,b) => a.name.localeCompare(b.name)) : state.galleries[sel] || []; dom.gallery.grid.innerHTML = ''; if (albums.length > 0) { dom.gallery.placeholder.style.display = 'none'; const frag = document.createDocumentFragment(); albums.forEach(a => { const c = document.createElement('div'); c.className = 'album-card'; c.dataset.path = a.path; c.innerHTML = `<img src="/image?path=${encodeURIComponent(a.cover)}" alt="${a.name}" loading="lazy" onerror="this.src='https://via.placeholder.com/250x350.png?text=No+Cover'"><div class="title">${a.name}</div>`; frag.appendChild(c) }); dom.gallery.grid.appendChild(frag) } else { dom.gallery.placeholder.style.display = 'flex' } };
    
    const connectWebSocket = () => {
        ws = new WebSocket(`${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws`);
        ws.onopen = () => { dom.loaderStatus.textContent = '连接成功，请求初始状态...'; ws.send(JSON.stringify({ action: 'get_initial_state' })); };
        ws.onclose = () => { dom.loader.classList.remove('hidden'); document.body.style.overflow = 'hidden'; dom.loaderStatus.textContent = '与服务器断开连接, 3秒后重连...'; setTimeout(connectWebSocket, 3000) };
        ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            switch (data.type) {
                case 'full_state':
                    dom.loader.classList.add('hidden'); document.body.style.overflow = 'auto';
                    updateStatusText(data.status, data.current_category);
                    setAppBusyState(data.is_crawling, data.is_organizing);
                    updateStats(data.stats); updateProgress(data.progress);
                    dom.concurrencySlider.value = data.concurrency; dom.concurrencyValue.textContent = data.concurrency;
                    break;
                case 'log': addLog(data.html, data.plain); break;
                case 'state_update':
                    if (data.status !== undefined) updateStatusText(data.status, data.current_category);
                    if (data.is_crawling !== undefined || data.is_organizing !== undefined) setAppBusyState(data.is_crawling, data.is_organizing);
                    if (data.stats) updateStats(data.stats); if (data.progress) updateProgress(data.progress);
                    if (data.concurrency) { dom.concurrencySlider.value = data.concurrency; dom.concurrencyValue.textContent = data.concurrency; }
                    break;
                case 'categories_list':
                    dom.categoryChecklist.innerHTML = '';
                    data.categories.forEach(c => { const id = `cat-${c}`; dom.categoryChecklist.innerHTML += `<label for="${id}"><input type="checkbox" id="${id}" value="${c}"> ${c}</label>`; });
                    break;
                case 'gallery_data':
                    state.galleries = data.galleries; const f = dom.gallery.filter; f.innerHTML = '<option value="all">所有</option>';
                    Object.keys(data.galleries).sort().forEach(c => { f.innerHTML += `<option value="${c}">${c}</option>`; });
                    renderGallery();
                    break;
            }
        };
    };
    
    const setupEventListeners = () => {
        dom.tabs.forEach(tab => tab.addEventListener('click', () => { dom.tabs.forEach(t => t.classList.remove('active')); dom.tabContents.forEach(c => c.classList.remove('active')); tab.classList.add('active'); $(`#${tab.dataset.tab}`).classList.add('active'); if (tab.dataset.tab === 'gallery') ws.send(JSON.stringify({ action: 'get_gallery_data' })) }));
        dom.startBtn.addEventListener('click', () => { const selected = Array.from($$('#category-checklist input:checked')).map(cb => cb.value); if (selected.length === 0) return addLog('<span style="color:var(--warning-color)">请至少选择一个分类！</span>', '请至少选择一个分类！'); ws.send(JSON.stringify({ action: 'start_crawl', categories: selected})) });
        dom.stopBtn.addEventListener('click', () => { ws.send(JSON.stringify({ action: 'stop_crawl' })) });
        dom.organizeBtn.addEventListener('click', () => { ws.send(JSON.stringify({ action: 'organize_files' })) });
        dom.concurrencySlider.addEventListener('input', e => { dom.concurrencyValue.textContent = e.target.value; });
        dom.concurrencySlider.addEventListener('change', e => { ws.send(JSON.stringify({ action: 'set_concurrency', value: parseInt(e.target.value) })) });
        dom.gallery.filter.addEventListener('change', renderGallery);
        dom.gallery.grid.addEventListener('click', async (e) => { const card = e.target.closest('.album-card'); if (!card) return; const response = await fetch(`/api/album?path=${encodeURIComponent(card.dataset.path)}`); if (response.ok) { const images = await response.json(); if (images.length > 0) { dom.lightbox.image.src = images[0]; dom.lightbox.container.classList.add('show'); } } });
        dom.lightbox.closeBtn.addEventListener('click', () => dom.lightbox.container.classList.remove('show'));
        dom.lightbox.container.addEventListener('click', e => { if (e.target === dom.lightbox.container) dom.lightbox.container.classList.remove('show'); });
    };

    document.addEventListener('DOMContentLoaded', () => { setupEventListeners(); connectWebSocket(); });
})();
</script>
</body>
</html>
"""

# --- Python Backend (Error Logging) ---
def clean_filename(text: str) -> str:
    return re.sub(r'[\\/*?:"<>|]', '_', text).strip()

async def init_db():
    async with aiosqlite.connect(DB_FILE) as db:
        await db.execute("""
            CREATE TABLE IF NOT EXISTS processed_urls (
                url TEXT PRIMARY KEY,
                album_title TEXT NOT NULL,
                processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        await db.commit()
    old_state_file = 'download_state.json'
    if os.path.exists(old_state_file):
        logger.info(f"发现旧的状态文件 '{old_state_file}'，开始迁移到SQLite数据库...")
        try:
            with open(old_state_file, 'r', encoding='utf-8') as f: data = json.load(f)
            if isinstance(data, dict) and 'processed_urls' in data:
                urls_to_migrate = [(url, status) for url, status in data['processed_urls'].items() if status not in ["Untitled", "unknown"]]
                if urls_to_migrate:
                    async with aiosqlite.connect(DB_FILE) as db:
                        await db.executemany("INSERT OR IGNORE INTO processed_urls (url, album_title) VALUES (?, ?)", urls_to_migrate)
                        await db.commit()
                    logger.success(f"成功迁移 {len(urls_to_migrate)} 条URL记录到数据库。")
            os.rename(old_state_file, f"{old_state_file}.migrated")
            logger.info(f"旧状态文件已重命名为 '{old_state_file}.migrated'。")
        except Exception as e:
            logger.error(f"从JSON文件迁移数据失败: {e}")

async def get_processed_urls_from_db() -> set:
    async with aiosqlite.connect(DB_FILE) as db:
        async with db.execute("SELECT url FROM processed_urls") as cursor:
            return {row[0] for row in await cursor.fetchall()}

async def add_url_to_db(url: str, album_title: str):
    async with aiosqlite.connect(DB_FILE) as db:
        await db.execute("INSERT OR REPLACE INTO processed_urls (url, album_title) VALUES (?, ?)", (url, album_title))
        await db.commit()

# 【新增】函数：记录失败的图集到JSON文件
async def log_failed_album(url: str, title: str, reason: str):
    async with error_log_lock:
        error_entry = {
            "url": url,
            "title": title,
            "reason": reason,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        try:
            if os.path.exists(ERROR_LOG_FILE):
                async with aiofiles.open(ERROR_LOG_FILE, 'r', encoding='utf-8') as f:
                    content = await f.read()
                    errors = json.loads(content) if content else []
            else:
                errors = []
            
            # 检查是否已记录过该URL
            if not any(e['url'] == url for e in errors):
                errors.append(error_entry)
                async with aiofiles.open(ERROR_LOG_FILE, 'w', encoding='utf-8') as f:
                    await f.write(json.dumps(errors, indent=4, ensure_ascii=False))
        except Exception as e:
            logger.error(f"写入错误日志 '{ERROR_LOG_FILE}' 失败: {e}")


async def update_shared_state(data_to_update: Dict):
    async with shared_state['lock']:
        for key, value in data_to_update.items():
            if isinstance(shared_state.get(key), dict) and isinstance(value, dict):
                shared_state[key].update(value)
            else:
                shared_state[key] = value
        await broadcast_queue.put({"type": "state_update", **data_to_update})

def parse_page_for_links(html_content: str, base_url: str) -> Tuple[List[Dict[str, str]], Optional[int]]:
    soup = BeautifulSoup(html_content, 'lxml')
    links, max_page = [], None
    for a in soup.select('a.thumbnail-link'):
        img = a.find('img')
        if img and not (a.find_parent('div', class_='post_thumb post_thumb_top') or (img.get('width') == '360' and img.get('height') == '540')):
            title = img.get('title')
            if not title or title.strip() == '':
                slug = a['href'].strip('/').split('/')[-1]
                decoded_slug = unquote(slug)
                title = decoded_slug.replace('-', ' ').replace('_', ' ').title()
            links.append({'url': urljoin(base_url, a['href']), 'title': title})
    page_numbers = soup.select('a.page-numbers')
    if page_numbers:
        nums = [int(p.text) for p in page_numbers if p.text.isdigit()]
        if nums:
            max_page = max(nums)
    return links, max_page

# ##########################################################################
# ######################### 【核心修改区域】 ###############################
# ##########################################################################

def parse_album_for_images(html_content: str) -> List[str]:
    """
    解析图集页面以提取所有图片链接。
    【已修改】此函数现在可以处理不带文件扩展名的图片URL。
    """
    soup = BeautifulSoup(html_content, 'lxml')
    urls = set()
    
    # 【已修改】使用更通用的后代选择器 (空格代替 >) 并移除了对文件扩展名的严格检查。
    # 这可以匹配您提供的HTML结构: <div class="separator"><a><img></a></div>
    # 同时也兼容其他可能的结构，如 WordPress 的 <figure class="wp-block-image">
    for img in soup.select('div.separator img, figure.wp-block-image img'):
        src = img.get('src') or img.get('data-src')
        
        # 【已修改】只要 src 属性存在且不为空，就认为它是有效的图片链接。
        if src:
            urls.add(src)
            
    return sorted(list(urls))

# ##########################################################################
# ######################### 【修改结束】 ###################################
# ##########################################################################


async def get_html_with_pyppeteer(url: str) -> str:
    if not shared_state.get("browser"): return ""
    page = None
    try:
        page = await shared_state["browser"].newPage()
        await page.goto(url, timeout=PYPPETEER_PAGE_TIMEOUT)
        # 等待JS执行，可以根据需要调整延时
        await asyncio.sleep(PYPPETEER_WAIT_DELAY)
        content = await page.content()
        return content
    except Exception as e:
        logger.error(f"Pyppeteer获取页面 {url} 失败: {e}")
        return ""
    finally:
        if page: await page.close()


async def download_image(url: str, client: httpx.AsyncClient, image_semaphore: asyncio.Semaphore, folder_path: str, album_name: str) -> Tuple[str, int]:
    filename = os.path.join(folder_path, f'_{url.split("/")[-1]}')
    try:
        if await aiofiles.os.path.exists(filename) and await aiofiles.os.path.getsize(filename) > 0:
            await stats_update_queue.put({'album_name': album_name, 'status': 'skipped', 'size': 0})
            return 'skipped', 0
    except OSError:
        pass
    async with image_semaphore:
        if not CRAWL_RUNNING.is_set():
            return 'cancelled', 0
        for _ in range(3):
            try:
                temp_file = filename + '.part'
                async with client.stream("GET", url, timeout=HTTP_TIMEOUT, follow_redirects=True) as r:
                    r.raise_for_status()
                    size = int(r.headers.get('content-length', 0))
                    async with aiofiles.open(temp_file, 'wb') as f:
                        async for chunk in r.aiter_bytes(65536):
                            await f.write(chunk)
                await aiofiles.os.rename(temp_file, filename)
                await stats_update_queue.put({'album_name': album_name, 'status': 'ok', 'size': size})
                return 'ok', size
            except Exception:
                await asyncio.sleep(2)
    await stats_update_queue.put({'album_name': album_name, 'status': 'failed', 'size': 0})
    return 'failed', 0

async def download_album(page_info: Dict, category: str, client: httpx.AsyncClient, image_concurrency: int, state_lock: asyncio.Lock) -> Tuple[bool, bool, str]:
    if not CRAWL_RUNNING.is_set(): return False, False, ""
    
    album_name = clean_filename(page_info['title'])
    
    try:
        resp = await client.get(page_info['url'], timeout=HTTP_TIMEOUT, follow_redirects=True)
        resp.raise_for_status()
        html_content = resp.text

        soup = BeautifulSoup(html_content, 'lxml')
        header_tag = soup.select_one('h1.entry-title, h2.single-post-title')
        if header_tag and header_tag.text.strip():
            album_name = clean_filename(header_tag.text.strip())
        
        folder_path = os.path.join(SAVE_DIR, category, album_name)
        if await aiofiles.os.path.exists(os.path.join(folder_path, '.download_complete')):
             await add_url_to_db(page_info['url'], album_name)
             return True, False, album_name
        
        urls = parse_album_for_images(html_content)

        if not urls and PYPPETEER_AVAILABLE:
            await log_to_ui(f"图集 [<b>{album_name}</b>] 初始解析失败, 启动浏览器模式重试...", m_type="warning")
            html_content = await get_html_with_pyppeteer(page_info['url'])
            if html_content:
                urls = parse_album_for_images(html_content)

        if not urls:
            await log_to_ui(f"图集 [<b>{album_name}</b>] 最终未能解析到任何有效图片链接，跳过。", m_type="error")
            await log_failed_album(page_info['url'], album_name, "解析失败") # 【修改】记录解析失败
            return False, False, album_name

        await aiofiles.os.makedirs(folder_path, exist_ok=True)
        await log_to_ui(f"图集 [<b>{album_name}</b>]: 发现 {len(urls)} 张新图", m_type="primary")
        async with state_lock:
            shared_state['progress'][album_name] = {'completed': 0, 'total': len(urls)}

        image_semaphore = asyncio.Semaphore(image_concurrency)
        tasks = [download_image(url, client, image_semaphore, folder_path, album_name) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        success_count = sum(1 for res in results if isinstance(res, tuple) and res[0] in ('ok', 'skipped'))

        async with state_lock:
            if album_name in shared_state['progress']:
                del shared_state['progress'][album_name]

        if success_count == len(urls):
            async with aiofiles.open(os.path.join(folder_path, '.download_complete'), 'w') as f: await f.write(time.strftime("%Y-%m-%d %H:%M:%S"))
            await add_url_to_db(page_info['url'], album_name)
            return True, True, album_name
        else:
            await log_to_ui(f"图集 [<b>{album_name}</b>] 部分失败", m_type="warning")
            await log_failed_album(page_info['url'], album_name, "部分下载失败") # 【修改】记录部分下载失败
            return False, False, album_name
    except Exception as e:
        logger.error(f"处理图集 '{album_name}' ({page_info['url']}) 失败: {e}")
        async with state_lock:
            if album_name in shared_state['progress']: del shared_state['progress'][album_name]
        await log_failed_album(page_info['url'], album_name, f"下载任务异常: {e}") # 【修改】记录异常
        return False, False, album_name

async def stats_aggregator_task():
    bytes_this_sec, last_update = 0, time.monotonic()
    while CRAWL_RUNNING.is_set() or not stats_update_queue.empty():
        try:
            item = await asyncio.wait_for(stats_update_queue.get(), timeout=1.0)
            status, size, name = item.get('status'), item.get('size', 0), item.get('album_name')
            async with shared_state['lock']:
                if name in shared_state['progress']: shared_state['progress'][name]['completed'] += 1
                if status == 'ok':
                    shared_state['stats']['totalImages'] += 1
                    shared_state['stats']['totalSize'] += size
                    bytes_this_sec += size
            stats_update_queue.task_done()
        except asyncio.TimeoutError: pass
        if (delta := time.monotonic() - last_update) >= 1.0:
            speed = bytes_this_sec / delta
            bytes_this_sec, last_update = 0, time.monotonic()
            async with shared_state['lock']:
                shared_state['stats']['speed'] = speed
                state_copy = {"progress": json.loads(json.dumps(shared_state['progress'])), "stats": json.loads(json.dumps(shared_state['stats']))}
            await broadcast_queue.put({"type": "state_update", **state_copy})
    await update_shared_state({"stats": {"speed": 0.0}})

async def album_downloader_worker(queue: asyncio.Queue, cat_name: str, client: httpx.AsyncClient, image_concurrency: int, state_lock: asyncio.Lock):
    while True:
        page_info = await queue.get()
        if page_info is None: break
        success, new_dl, _ = await download_album(page_info, cat_name, client, image_concurrency, state_lock)
        if success and new_dl:
            async with state_lock: shared_state['stats']['totalAlbums'] += 1
        queue.task_done()

async def scan_category_page(url: str, client: httpx.AsyncClient, processed_urls: set, album_queue: asyncio.Queue, page_num: int, cat_name: str):
    try:
        resp = await client.get(url, follow_redirects=True)
        resp.raise_for_status()
        pages, _ = parse_page_for_links(resp.text, url)
        new_pages = [p for p in pages if p['url'] not in processed_urls]
        await log_to_ui(f"并发扫描 <b>{cat_name}</b> 第 {page_num} 页... 新增 {len(new_pages)} 个")
        for page in new_pages:
            await album_queue.put(page)
    except Exception as e:
        logger.error(f"并发扫描页面 {url} 失败: {e}")

async def crawl_task(categories_to_crawl: List[str]):
    CRAWL_RUNNING.set()
    await update_shared_state({"status": "运行中", "is_crawling": True})
    await log_to_ui("爬虫任务启动...", m_type="success")
    
    if PYPPETEER_AVAILABLE and not shared_state.get("browser"):
        await log_to_ui("正在启动浏览器实例...", m_type="primary")
        try:
            shared_state["browser"] = await pyppeteer.launch(headless=True, args=['--no-sandbox'])
        except Exception as e:
            logger.error(f"启动Pyppeteer失败: {e}")
            await log_to_ui("启动浏览器失败，将不使用动态解析功能。", m_type="error")

    aggregator = asyncio.create_task(stats_aggregator_task())
    processed_urls = await get_processed_urls_from_db()
    
    async with shared_state['lock']: 
        image_concurrency = shared_state['concurrency']
        state_lock = shared_state['lock']

    headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'}
    async with httpx.AsyncClient(http2=True, verify=False, headers=headers, timeout=HTTP_TIMEOUT) as client:
        album_queue = asyncio.Queue(maxsize=MAX_CONCURRENT_ALBUMS * 2)

        for cat_name in categories_to_crawl:
            if not CRAWL_RUNNING.is_set(): break
            base_url = CATEGORIES.get(cat_name)
            if not base_url: continue

            await update_shared_state({"current_category": cat_name})
            await aiofiles.os.makedirs(os.path.join(SAVE_DIR, cat_name), exist_ok=True)
            await log_to_ui(f"--- 开始处理分类: <b>{cat_name}</b> ---", m_type="primary")

            downloader_tasks = [
                asyncio.create_task(album_downloader_worker(album_queue, cat_name, client, image_concurrency, state_lock))
                for _ in range(MAX_CONCURRENT_ALBUMS)
            ]

            try:
                resp = await client.get(base_url, follow_redirects=True)
                resp.raise_for_status()
                pages, max_pages = parse_page_for_links(resp.text, base_url)
                
                if max_pages:
                    await log_to_ui(f"<b>{cat_name}</b>: 检测到 {max_pages} 页, 开始并发扫描...", m_type="info")
                    new_pages = [p for p in pages if p['url'] not in processed_urls]
                    for page in new_pages: await album_queue.put(page)
                    scan_tasks = []
                    semaphore = asyncio.Semaphore(PAGE_SCAN_CONCURRENCY)
                    async def throttled_scan(page_num):
                        async with semaphore:
                            if not CRAWL_RUNNING.is_set(): return
                            page_url = f"{base_url}page/{page_num}/"
                            await scan_category_page(page_url, client, processed_urls, album_queue, page_num, cat_name)
                    
                    for page_num in range(2, max_pages + 1):
                        scan_tasks.append(throttled_scan(page_num))
                    
                    await asyncio.gather(*scan_tasks)
                else:
                    await log_to_ui(f"<b>{cat_name}</b>: 未检测到分页，仅处理第一页。", m_type="warning")
                    for p in pages: await album_queue.put(p)
            except Exception as e:
                logger.error(f"扫描分类 {cat_name} 首页失败: {e}")
            
            await album_queue.join()
            for _ in range(MAX_CONCURRENT_ALBUMS): await album_queue.put(None)
            await asyncio.gather(*downloader_tasks)
    
    if shared_state.get("browser"):
        await log_to_ui("正在关闭浏览器实例...", m_type="primary")
        await shared_state["browser"].close()
        shared_state["browser"] = None

    await log_to_ui("所有任务完成!", m_type="success")
    await update_shared_state({"status": "完成", "is_crawling": False, "current_category": ""})
    CRAWL_RUNNING.clear()
    await aggregator

async def organize_files_task():
    await update_shared_state({"status": "整理中...", "is_organizing": True})
    await log_to_ui("整理功能暂未完全适配数据库，可能导致状态不一致。请谨慎使用。", m_type="warning")
    await update_shared_state({"status": "整理完成", "is_organizing": False})

async def broadcaster():
    while True:
        try:
            msg = await asyncio.wait_for(broadcast_queue.get(), timeout=0.2)
            await ws_manager.broadcast(msg)
            broadcast_queue.task_done()
        except asyncio.TimeoutError: pass

def get_initial_stats_sync():
    stats = {'totalAlbums': 0, 'totalImages': 0, 'totalSize': 0}
    if not os.path.exists(SAVE_DIR): return stats
    for root, dirs, _ in os.walk(SAVE_DIR):
        dirs[:] = [d for d in dirs if d not in CATEGORIES]
        for album in dirs:
            album_path = os.path.join(root, album)
            if os.path.exists(os.path.join(album_path, '.download_complete')):
                stats['totalAlbums'] += 1
                for f in os.listdir(album_path):
                    if f.lower().endswith(('.jpg', '.jpeg', '.png', '.webp')) or not '.' in f: # 包含无扩展名的文件
                        try:
                            stats['totalImages'] += 1
                            stats['totalSize'] += os.path.getsize(os.path.join(album_path, f))
                        except OSError: pass
    return stats

def scan_gallery_sync():
    galleries = {}
    if not os.path.isdir(SAVE_DIR): return galleries
    for cat in list(CATEGORIES.keys()):
        cat_path = os.path.join(SAVE_DIR, cat)
        if os.path.isdir(cat_path):
            albums = []
            for album in sorted(os.listdir(cat_path)):
                album_path = os.path.join(cat_path, album)
                if os.path.isdir(album_path) and os.path.exists(os.path.join(album_path, '.download_complete')):
                    images = sorted([f for f in os.listdir(album_path) if f != '.download_complete'])
                    if images:
                        cover = next((img for img in images if not img.startswith('_')), images[0])
                        albums.append({"name": album, "path": os.path.join(cat, album), "cover": os.path.join(cat, album, cover)})
            if albums: galleries[cat] = albums
    return galleries

@app.on_event("startup")
async def startup_tasks():
    await init_db()
    asyncio.create_task(broadcaster())
    if not PYPPETEER_AVAILABLE:
        logger.warning("Pyppeteer未安装, 动态内容解析功能将不可用。请运行 'pip install pyppeteer'。")

@app.get("/", response_class=HTMLResponse)
async def get_root(): return HTML_TEMPLATE

@app.get("/image")
async def get_image(path: str):
    try:
        safe_path = os.path.abspath(os.path.join(SAVE_DIR, unquote(path)))
        if os.path.commonpath([safe_path, os.path.abspath(SAVE_DIR)]) != os.path.abspath(SAVE_DIR): return Response(status_code=403)
        return FileResponse(safe_path) if os.path.exists(safe_path) else Response(status_code=404)
    except Exception: return Response(status_code=404)

@app.get("/api/album")
async def get_album_images(path: str):
    album_path = os.path.join(SAVE_DIR, unquote(path))
    if not os.path.isdir(album_path): return []
    images = sorted([f for f in os.listdir(album_path) if f != '.download_complete'])
    return ["/image?path=" + quote(os.path.join(unquote(path), img), safe='/?=&') for img in images]

async def get_current_app_state() -> Dict:
    async with shared_state['lock']: return json.loads(json.dumps({k: v for k, v in shared_state.items() if k not in ['lock', 'browser']}))

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await ws_manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_json()
            action = data.get('action')
            if action == "get_initial_state":
                await websocket.send_json({"type": "categories_list", "categories": list(CATEGORIES.keys())})
                stats = await asyncio.to_thread(get_initial_stats_sync)
                async with shared_state['lock']: shared_state['stats'].update(stats)
                state = await get_current_app_state()
                await websocket.send_json({"type": "full_state", **state})
            else:
                async with shared_state['lock']: is_busy = shared_state['is_crawling'] or shared_state['is_organizing']
                if action == "start_crawl" and not is_busy:
                    asyncio.create_task(crawl_task(data.get('categories', [])))
                elif action == "stop_crawl" and shared_state['is_crawling']:
                    await log_to_ui("收到停止指令...", m_type="warning")
                    CRAWL_RUNNING.clear()
                elif action == "organize_files" and not is_busy:
                    asyncio.create_task(organize_files_task())
                elif action == "set_concurrency" and not is_busy:
                    await update_shared_state({'concurrency': int(data.get('value', DEFAULT_IMAGE_CONCURRENCY))})
                elif action == "get_gallery_data":
                    gallery_data = await asyncio.to_thread(scan_gallery_sync)
                    await websocket.send_json({"type": "gallery_data", "galleries": gallery_data})
    except WebSocketDisconnect: logger.info("客户端断开连接")
    except Exception as e: logger.warning(f"WebSocket 错误: {e}")
    finally: ws_manager.disconnect(websocket)

if __name__ == "__main__":
    print("="*60)
    print(" Everia Crawler V17.2 (Fix, Robust Parser)")
    print("="*60)
    print(" 启动成功! 请在浏览器中打开 http://127.0.0.1:8000")
    uvicorn.run(app, host="127.0.0.1", port=8000, log_level="warning")